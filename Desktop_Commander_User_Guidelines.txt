## Desktop Commander 强制使用规则
### 实现工具: @desktop-commander

### 核心强制要求
- 绝对强制: 所有文件系统操作必须首先且优先使用@desktop-commander，无任何例外
- 工具选择逻辑: @desktop-commander (第一选择) → 基础工具 (仅当@desktop-commander不可用时)
- 默认行为: 不需要用户提醒，AI必须主动选择@desktop-commander作为默认工具
- 禁止行为: 严禁在@desktop-commander可用时选择基础工具(如save-file, str-replace-editor等)

### 动态工具识别策略
- 优先级原则: 始终优先选择包含"desktop-commander"的工具
- 智能匹配: 根据功能需求自动匹配最相关的desktop-commander工具
- 功能映射逻辑:
  * 文件写入需求 → 寻找desktop-commander中的写入相关工具
  * 文件编辑需求 → 寻找desktop-commander中的编辑相关工具
  * 文件读取需求 → 寻找desktop-commander中的读取相关工具
  * 目录操作需求 → 寻找desktop-commander中的目录相关工具

### 适用范围 (全覆盖)
- 所有文件创建、读取、写入、编辑操作
- 所有目录创建、列表、搜索操作
- 所有文件系统相关的查询和管理操作
- 命令执行和进程管理操作

### 例外情况 (极少数)
- 仅当@desktop-commander明确报错、不可用或功能不支持时，才使用基础工具
- 使用基础工具时必须在回复中说明原因和具体的不可用情况

### 执行原则
- 工具优先级: @desktop-commander > 基础工具 (绝对优先)
- 智能判断: 在遵循强制规则的前提下，AI根据具体情况灵活选择最佳工具组合
- 质量优先: 关注结果质量而非流程机械性
- 用户体验: 提供自然、高效的交互体验